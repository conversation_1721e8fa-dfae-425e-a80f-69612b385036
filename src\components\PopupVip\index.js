import React from 'react'
import { Provider, connect } from 'react-redux'
import qs from 'qs'
import Script from 'react-load-script'
import { rebuildCommonUrl, hasAgentType, rebuildHttpsUrl } from '@/kit/common'
import { platformId as getPlatformId } from '@/kit/common'
import { getUserInfoAction } from '@/store/reducers/user'
import $http from '@/kit/fetch'
import { getCookies, delClientCookies } from '@/kit/cookie'
import { getTimeZone } from '@/utils/common'
import { sendQosLog, calCE } from '@/kit/qosTag'
import { bankLangPkg } from './config'
import {
  SUBMIT_PAY_PHONE,
  bankDoPayInterface,
  MODE_PTID,
  LANG_PKG,
  FUNCS_API,
  INIT_3DS_VERIFY,
  STAY_USER
} from '@/constants/interfaces'
// import DeviceCtx from '@/components/context'
import { isLogin, getUid } from '@/utils/userInfo'
import { pause, resume, savePlayerObj } from '@/utils/player/handlePlayerEvent'
// import UserWrap from '@/components/common/Header/userWrap'
import { sendBlockPb, sendClickPb } from '@/utils/pingBack'
import { getDevice, iqSwitchPlatformId } from '@/kit/device'
import { initLogin, checkReNew } from '@/utils/loginWindow.js'
import store from '@/store'
import VipOrder from './VipOrder'
import Vod from './Vod'
import Style from './style'
import GlobalStyle from './style/reset.js'
import MobilePage from './Mobile'
import TabContent from './TabContent'
import DSModal from './DSModal'
import QueryPayment from './QueryPayment'
import PayResult from './PayResult'
import NewPayResult from './NewPayResult'
import TemplateErrorPop from './Error'
import PayType from './PayType'
import BankCard from './BankCard'
import Gopay from './Gopay'
import StayPop from './StayPop'
import {
  getVodData,
  getPayRediectUrl,
  getVipData,
  newgetVipData,
  handleVipInfo,
  newHandleVipInfo,
  createOrder,
  getAbtest,
  getUserCard,
  getMobileUrl,
  getRetentionData,
  hasShownRetentionInSession,
  clearRetentionSession
} from './api'
import { queryFromUrl } from '@/kit/url'

const PAGE_MAP = {
  vodOrderStep: {
    name: 'vodOrderStep',
    last: ''
  },
  vipOrderStep: {
    name: 'vipOrderStep',
    last: ''
  },
  payTypeStep: {
    name: 'payTypeStep',
    last: 'vipOrderStep'
  },
  setMobileStep: {
    name: 'setMobileStep',
    last: { 1: 'payTypeStep', 2: 'vodOrderStep' }
  },
  queryPaymentStep: {
    name: 'queryPaymentStep',
    last: { 1: 'payTypeStep', 2: 'vodOrderStep' }
  },
  payResultStep: {
    name: 'payResultStep'
  },
  bankCardStep: {
    name: 'bankCardStep',
    last: { 1: 'payTypeStep', 2: 'vodOrderStep' }
  }
}

const createModal = (Component) => {
  const ref = React.createRef()

  const showVipPop = async (params) => {
    if (window) {
      window.globalVipCE =
        (window._performance && window._performance.eventId) || calCE() || ''
    }
    const lang = getCookies('lang') || 'en_us'
    const deviceId = getCookies('QC005')
    const mod = getCookies('mod') || 'intl'
    const platformId = getPlatformId()
    // let controlData = null
    ref.current.show() // 先加载骨架屏
    try {
      sendQosLog({
        diy_evt: 'pop_langPkg_start',
        ce: window && window.globalVipCE
      })
    } catch (err) {
      console.log(err)
    }

    const langPkgT = Date.now()
    try {
      const [ptidData, vipPkgData, cashierPkgData, switchData] =
        await Promise.all([
          $http(MODE_PTID, {
            params: {
              langCode: lang,
              deviceId: deviceId,
              modeCode: mod,
              platformId
            }
          }),
          $http(LANG_PKG, {
            params: {
              langCode: lang,
              businessName: 'PCW_VIP',
              platformId
            }
          }),
          $http(LANG_PKG, {
            params: {
              langCode: lang,
              businessName: 'PCW_CASHIER',
              platformId
            }
          }),
          $http(FUNCS_API, {
            params: {
              platform_id: iqSwitchPlatformId(),
              app_v: '1.0.0',
              lang: lang,
              app_lm: mod // 这里先写死
            }
          })
        ])
      if (ptidData.code === '0') {
        const requsetData = ptidData.data
        params = Object.assign(params, {
          ptid: requsetData.ptid,
          vipPid: requsetData.cashierConfig,
          bossCode: requsetData.bossCode
        })
      }
      if (vipPkgData.code === '0') {
        params.vipLangPkg = vipPkgData.data
      }
      if (cashierPkgData.code === '0') {
        params.cashierLangPkg = cashierPkgData.data
      }
      if (switchData.code === 0) {
        params.switchData = switchData.data
        // controlData = switchData.data
      }
      try {
        sendQosLog({
          diy_evt: 'pop_langPkg_end',
          ce: window.globalVipCE,
          tm: Date.now() - langPkgT,
          diy_success: 1
        })
      } catch (err) {
        // 静默处理错误
      }
    } catch (e) {
      // 静默处理错误
      sendQosLog({
        diy_evt: 'pop_langPkg_end',
        tm: Date.now() - langPkgT,
        diy_success: 0,
        ce: window.globalVipCE,
        diy_msg: 'langPkg Err' + e
      })
    }
    params.isLiveTvod = params.fc === '889ecddd9e3af059'
    // 兼容老参数
    if (params.vipType === undefined && params.productName) {
      params.vipType = params.vipPid[params.productName].viptype
    }
    let cookieFC = getCookies('QCedm001')
    let cookieFV = getCookies('QCedm002')

    let fcList = (params.fc && params.fc.split(',')) || []
    if (fcList.indexOf(cookieFC) < 0 && cookieFC) {
      params.fc = `${params.fc ? params.fc + ',' : ''}${cookieFC}`
      params.fv = cookieFV
    }

    ref.current.setParams(params)

    if (params.cashierType === 2) {
      ref.current.getVodData(params)
    } else {
      params.startT = langPkgT
      ref.current.newGetVipData(params)
    }
    if (!window.sdkPackManager) {
      initLogin()
    } else if (isLogin()) {
      checkReNew(params.ptid)
      return
    }
  }
  const createVipOrder = async (params = {}) => {
    // abtest
    // http://wiki.qiyi.domain/pages/viewpage.action?pageId=881232152 透传参数
    const fvCookie = getCookies('playFV')
    params.fv = fvCookie || params.fv || ''
    const paramsKey = [
      'vipType',
      'fc',
      'fv',
      'amount',
      'payAutoRenew',
      'albumId',
      'fr',
      'abtest',
      'productName'
    ]
    let urlQuery = ''
    paramsKey.forEach((key) => {
      if (params[key] !== undefined) {
        urlQuery +=
          urlQuery.length === 0
            ? `${key}=${params[key]}`
            : `&${key}=${params[key]}`
      }
    })
    if (window.gtag) {
      window.gtag('event', 'visit_cashier', {
        mod: getCookies('mod') || 'intl',
        rpage: 'cashier_popup'
      })
    }
    const url = urlQuery.length !== 0 ? 'vip/order?' + urlQuery : 'vip/order'
    if (params.cashierType && parseInt(params.cashierType, 10) === 2) {
      if (ref.current) {
        await showVipPop(params)
      }
    } else if (
      getDevice() === 'mobile' ||
      (params.cashierType && parseInt(params.cashierType, 10) === 1)
    ) {
      window.location.href = rebuildCommonUrl(url)
      // else {
      //   await showVipPop(params)
      // }
    } else {
      if (ref.current) {
        await showVipPop(params)
      }
    }
  }
  return {
    PopupVip: (props) => (
      <Provider store={store}>
        <Component {...props} ref={ref} createVipOrder={createVipOrder} />
      </Provider>
    ),
    createVipOrder,
    hidePopup: () => {
      ref.current.hide()
    }
  }
}
export class VipModal extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      visible: false,
      step: '',
      errorType: '',
      params: {},
      vipInfo: null,
      vodVipInfo: null,
      resultInfo: {},
      mobile: '',
      createOrderError: '',
      isError: false,
      resultSuccess: false,
      canPlay: true, // 播放页是否可以播放
      createPayTypeError: '',
      selectedPkg: {},
      currentPayTypeOptions: [],
      isAgree: false,
      checkErr: false,
      lang: getCookies('lang') || 'en_us',
      mod: getCookies('mod'),
      payTypeOption: {},
      productSetCode: undefined,
      defaultVipModalInfo: undefined,
      orderInfo: undefined,
      isNewVip: false,
      // 弹窗数据加载
      popLoading: true,
      defaultCoupon: '',
      // 展示支付结果
      showPayResult: 'pkgSelect',
      resultType: '',
      // 展示解冻
      showFreeze: false,
      dopayError: '',
      gopayError: '',
      createOrdering: false,
      gopayLoading: false,
      // 判断是不是兑换优惠券之后的请求
      hasRedeemed: false,
      // 挽留弹窗
      showStayPop: false,
      stayData: null,
      userCards: null,
      show3DSModal: false,
      // 3ds验证接口返回的数据
      DSResData: ''
    }
  }

  componentDidMount() {
    // http://wiki.qiyi.domain/pages/viewpage.action?pageId=1013188438 记忆登录前套餐选择
    const refreshParams = localStorage.getItem('vipPopupState')
    const vipModalInfoCookie = getCookies('vip_modal_info')
    const params = refreshParams ? JSON.parse(refreshParams) : {}
    const productSetCode = vipModalInfoCookie
      ? JSON.parse(vipModalInfoCookie).productSetCode
      : ''
    const defaultVipModalInfo = vipModalInfoCookie
      ? JSON.parse(vipModalInfoCookie)
      : ''

    // params.defaultVipModalInfo = defaultVipModalInfo
    const { createVipOrder, dispatch } = this.props
    if (vipModalInfoCookie) {
      this.setState({
        productSetCode,
        defaultVipModalInfo
      })
      delClientCookies('vip_modal_info')
      setTimeout(() => {
        createVipOrder(params)
        this.clearLocalState()
      })
    }
    if (isLogin()) {
      dispatch(getUserInfoAction())
    }
    this.watchCanPlay()
  }

  componentWillUnmount() {
    localStorage.setItem('vipPopupState', '')
  }

  // 在弹出vip蒙层的时候不自动继续播放
  watchCanPlay = () => {
    if (window.location.pathname.match(/\/play\//)) {
      if (window.QiyiPlayerLoaderIbd) {
        window.QiyiPlayerLoaderIbd.ready((qiyiPlayerManager) => {
          qiyiPlayerManager.players.flashbox &&
            qiyiPlayerManager.players.flashbox.on(
              qiyiPlayerManager.constantType.QYPLAYER_RECHARGE,
              (info) => {
                savePlayerObj(
                  window.QiyiPlayerLoaderIbd.manager.getPlayerById('flashbox')
                )
                if (info.type === 'recharge' && info.data.from !== 2) {
                  this.setState({
                    canPlay: false
                  })
                }
              }
            )
        })
      } else {
        setTimeout(this.watchCanPlay, 1000)
      }
    }
  }

  pausePlay = () => {
    if (window.location.pathname.match(/\/play\//)) {
      setTimeout(pause)
      if (window.QiyiPlayerLoaderIbd) {
        window.QiyiPlayerLoaderIbd.ready((qiyiPlayerManager) => {
          qiyiPlayerManager.players.flashbox.on(
            qiyiPlayerManager.constantType.QYPLAYER_STATE_CHANGE,
            (data) => {
              const _type = data.data.state || ''
              if (_type === 'startplay' && this.state.visible) {
                setTimeout(pause)
              }
            }
          )
        })
      } else {
        setTimeout(this.pausePlay, 1000)
      }
    }
  }

  show = () => {
    // 播放器暂停
    this.pausePlay()

    if (document) {
      document.body.style.height = '100%'
      document.body.style.overflowY = 'hidden'
    }

    // 清理上次会话的挽留弹窗展示记录
    clearRetentionSession()

    this.setState({
      visible: true
    })
  }

  clearError = () => {
    this.setState({
      gopayError: '',
      dopayError: ''
    })
  }

  // 请求挽留弹窗接口 - 改进版本
  getStayData = async () => {
    const { lang, mod } = this.state

    try {
      const retentionData = await getRetentionData(lang, mod)

      if (retentionData) {
        this.setState({
          showStayPop: true,
          stayData: retentionData
        })
        return false // 阻止关闭收银台
      } else {
        return true // 允许关闭收银台
      }
    } catch (error) {
      console.error('获取挽留弹窗数据失败:', error)
      return true // 出错时允许关闭收银台
    }
  }

  // 处理挽留弹窗的"留下"按钮点击
  handleStayRetention = () => {
    const { stayData } = this.state

    // 关闭挽留弹窗，用户停留在收银台
    this.setState({
      showStayPop: false
    })

    // 如果有fc参数，需要重置
    if (stayData && stayData.fc) {
      this.resetFC(stayData.fc)
    }
  }

  // 处理挽留弹窗的"离开"按钮点击
  handleLeaveRetention = () => {
    // 关闭挽留弹窗，并关闭整个收银台
    this.setState({
      showStayPop: false
    })

    // 延迟关闭收银台，确保弹窗动画完成
    setTimeout(() => {
      this.hide('leave')
    }, 100)
  }

  // 兼容旧版本的closeStayPop方法
  closeStayPop = (fc) => {
    this.handleStayRetention()
  }

  resetFC = (fc) => {
    const { params } = this.state
    let fcList = (params.fc && params.fc.split(',')) || []
    if (fcList.indexOf(fc) < 0 && fc) {
      this.setState({
        params: {
          ...params,
          fc: `${params.fc ? params.fc + ',' : ''}${fc}`
        }
      })
    }
  }

  hide = async (clkType) => {
    const { resultSuccess, step, canPlay, isNewVip, params } = this.state
    let goOn = true

    // 只在弹窗收银台（非落地页）且点击关闭按钮时触发挽留弹窗
    const isCashierPopup = params.cashierType !== '1' && getDevice() === 'pc'
    const isMainCloseClick = clkType === 'main'

    if (isNewVip && isCashierPopup && isMainCloseClick) {
      goOn = await this.getStayData()
      if (!goOn) {
        return
      }
    }
    // 播放器播放
    if (canPlay) {
      resume()
    }

    if (document) {
      document.body.style.height = ''
      document.body.style.overflowY = ''
    }

    this.setState({
      popLoading: true,
      defaultVipModalInfo: undefined,
      vipInfo: null,
      visible: false,
      step: '',
      isError: false,
      errorType: '',
      currentPayTypeOptions: [],
      selectedPkg: {},
      showPayResult: 'pkgSelect',
      dopayError: '',
      gopayError: '',
      isNewVip: false,
      showFreeze: false,
      createOrdering: false,
      gopayLoading: false,
      showStayPop: false,
      stayData: null
    })
    this.clearLocalState()
    // 购买成功之后刷新页面
    // eslint-disable-next-line
    delete window.isRefresh
    resultSuccess && window.location.reload()
  }

  setParams = (params) => {
    params.cashierType = Number(params.cashierType)
    if (params) {
      this.setState({
        params: {
          ...params
        },
        step:
          params.cashierType === 2
            ? PAGE_MAP.vodOrderStep.name
            : PAGE_MAP.vipOrderStep.name,
        defaultCoupon: params.couponCode || ''
      })
    }
  }

  setStep = (param) => {
    if (
      [PAGE_MAP.vodOrderStep.name, PAGE_MAP.vipOrderStep.name].includes(param)
    ) {
      this.setState({
        currentPayTypeOptions: []
      })
    }
    this.setState({ step: param })
  }

  getVodData = async (params) => {
    const initParams = {
      ...this.state.params,
      ...params
    }
    const { lang, mod } = this.state
    try {
      const data = await getVodData(initParams, lang, mod)
      this.setState({
        vodVipInfo: data
      })
    } catch (err) {
      // 静默处理错误
      if (err.message.match('timeout')) {
        this.setErrorPop('timeout')
      } else {
        this.setErrorPop()
      }
    }
  }

  getVipData = async (params = {}) => {
    const initParams = {
      ...this.state.params,
      ...params
    }

    const { lang, mod } = this.state
    // try {
    //   const vipInfoRes = await getVipData(initParams, lang, mod)
    //   const vipInfo = handleVipInfo(vipInfoRes, this.state.params.vipLangPkg)
    //   this.setState({
    //     vipInfo,
    //     isAgree: vipInfo.isSelectVipServiceAgreement
    //   })
    // } catch (err) {
    //   console.log(err)
    //   if (err.message.match('timeout')) {
    //     this.setErrorPop('timeout')
    //   } else {
    //     this.setErrorPop()
    //   }
    // }
  }

  newGetVipData = async (params = {}, couponCode) => {

    const startT = params.startT
    delete params.startT
    const initParams = {
      ...this.state.params,
      ...params
    }
    this.setState({
      isNewVip: true,
      popLoading: true
    })

    // if (couponCode) return
    const checkoutT = Date.now()
    const { lang, mod, defaultCoupon } = this.state

    const { switchData } = initParams
    const { i18n_switch_cashier_show_coupon, aniPopCount } = switchData
    try {
      // const vipInfoRes = await getVipData(initParams, lang, mod)
      try {
        sendQosLog({
          diy_evt: 'pop_checkout_start',
          ce: window.globalVipCE
        })
      } catch (_err) {
        // 静默处理错误
      }

      const newVipInfoRes = await newgetVipData(initParams, lang, mod)
      let hasCard = false
      if (isLogin()) {
        const userCardData = await getUserCard()
        if (userCardData.code === 'A00000') {
          const { data } = userCardData
          hasCard = data.length > 0
          this.setState({ userCards: data.slice(0, 5) })
        }
      } else {
        this.setState({ userCards: null })
      }
      let resData = null
      if (newVipInfoRes.code === 'A00000') {
        try {
          sendQosLog({
            diy_evt: 'pop_checkout_end',
            ce: window.globalVipCE,
            tm: Date.now() - checkoutT,
            diy_success: 1
          })
        } catch (_err) {
          console.log(_err)
        }

        resData = newVipInfoRes.data
        const vipInfo = newHandleVipInfo(
          resData,
          this.state.params.vipLangPkg,
          i18n_switch_cashier_show_coupon,
          couponCode || defaultCoupon,
          params.defaultVipModalInfo,
          hasCard
        )
        try {
          sendQosLog({
            diy_evt: 'pop_handleData_end',
            tm: Date.now() - startT
          })
        } catch (err) {
          console.log(err)
        }

        sendBlockPb('cashier_popup', {
          bstp: 56,
          tjPb: { abtest: vipInfo.groupCode }
        })
        if (couponCode || defaultCoupon) {
          this.setState({
            hasRedeemed: true
          })
        } else {
          this.setState({
            hasRedeemed: false
          })
        }
        // console.log(newVipInfoRes, 'in pop vip get vip data')
        this.setState({
          vipInfo: vipInfo,
          popLoading: false
          // isAgree: vipInfo.isSelectVipServiceAgreement
        })

        // sendBlockPb('cashier_popup', { tjPb: { abtest: vipInfo.groupCode } })
      } else {
        try {
          sendQosLog({
            diy_evt: 'pop_checkout_end',
            tm: Date.now() - checkoutT,
            ce: window.globalVipCE,
            diy_success: 0,
            diy_msg: newVipInfoRes.code
          })
        } catch (_err) {
          console.log(_err)
        }

        if (newVipInfoRes.code === 'Q00301') {
          this.setState({
            resultType: 'system',
            showPayResult: 'result'
          })
        }
      }
    } catch (err) {
      sendQosLog({
        diy_evt: 'pop_checkout_end',
        tm: Date.now() - checkoutT,
        ce: window.globalVipCE,
        diy_success: 0,
        diy_msg: 'checkout Err ==>' + err
      })
      console.log(err)
      if (!window.navigator.onLine) {
        this.setState({
          resultType: 'neterr',
          showPayResult: 'result'
        })
      } else if (err.message.match('timeout')) {
        this.setErrorPop('timeout')
        this.setState({
          resultType: 'neterr',
          showPayResult: 'result'
        })
      } else {
        this.setErrorPop()
        this.setState({
          resultType: 'system',
          showPayResult: 'result'
        })
      }
    }
  }
  // handle

  // 在localStorage保存当前state，为了页面刷新后继续展示
  setStateInLocal = () => {
    const { params } = this.state
    delete params.ptid
    delete params.vipPid
    delete params.bossCode
    delete params.vipLangPkg
    delete params.cashierLangPkg
    localStorage.setItem('vipPopupState', JSON.stringify(params))
  }

  clearLocalState = () => {
    localStorage.setItem('vipPopupState', '')
  }

  handleVodPayOrder = async (data, selectedType) => {
    const initParams = this.state.params
    const { lang, mod, vodVipInfo } = this.state

    const { product, packageInfo } = vodVipInfo

    this.setState({
      resultInfo: {
        vipOrder: data.orderCode,
        platform: initParams.bossCode,
        lang,
        mod,
        timeZone: getTimeZone(mod)
      }
    })

    let openUrl
    let orderInfo
    let nextStep = PAGE_MAP.queryPaymentStep.name
    if (data.redirectUrl) {
      openUrl = data.redirectUrl
    } else {
      orderInfo = {
        typeName: '',
        vipOrder: data.vipOrder,
        order: data.order,
        name: initParams.title || '',
        currencySymbol: product.currencySymbol,
        price: packageInfo.vodPrice,
        originalPrice: packageInfo.vodOriginPrice,
        autorenewTip: '',
        detail: '',
        cashierType: initParams.cashierType
      }
      if (getAbtest() === 'popup_test_a' || getDevice() === 'mobile') {
        openUrl = getPayRediectUrl(orderInfo, initParams.isLiveTvod)
      } else {
        nextStep = PAGE_MAP.bankCardStep.name
      }
    }

    if (openUrl) {
      const newWin = window.open(openUrl)
      newWin.opener = null
    }
    this.setState({
      resultInfo: {
        vipOrder: data.vipOrder,
        platform: initParams.bossCode,
        lang,
        mod,
        timeZone: getTimeZone(mod)
      },
      orderInfo,
      payTypeOption: selectedType,
      step: nextStep
    })
  }

  handlePaytypeSubmit = async () => {
    const { selectedPkg, isAgree, currentPayTypeOptions } = this.state
    const initParams = this.state.params
    // const modeLangObj = this.props.modeLangObj.toJS()
    const payTypeOption = currentPayTypeOptions.find((item) => {
      return item.recommend === 1
    })
    const payType = payTypeOption.payType

    if (!isAgree) {
      this.setState({
        checkErr: true
      })
      return
    }

    // 加线上日志
    const addloggers = {
      popupDopayLogin: {
        time: new Date(),
        uid: getUid(),
        isLogin: isLogin(),
        loginSdk: !window.sdkPackManager
      }
    }
    const loggers = localStorage.getItem('QiyiPlayerLogger')
    window.localStorage.setItem(
      'QiyiPlayerLogger',
      loggers + JSON.stringify(addloggers)
    )

    if (!isLogin()) {
      if (!window.sdkPackManager) {
        await initLogin()
      } else if (!window.sdkPackManager.globalLogin) {
        await window.sdkPackManager.initLogin()
      }
      window.isRefresh = () => {
        window.location.reload()
      }
      window.sdkPackManager.globalLogin.openLoginRegWindow()
      // this.setStateInLocal()
      return
    }

    this.handleCreateVipOrder(selectedPkg, payTypeOption)
  }

  handleCreateVipOrder = async (selectedPkg, payTypeOption) => {
    const { lang, mod, vipInfo } = this.state
    const initParams = this.state.params
    const abtest = getAbtest()
    const params = {
      pid: selectedPkg.pid,
      payType: payTypeOption.payType,
      amount: selectedPkg.amount,
      bossCode: initParams.bossCode,
      payAutoRenew: selectedPkg.payAutoRenew,
      aid: initParams.aid || '',
      fc: initParams.fc || '',
      fv: initParams.fv || '',
      abtest: abtest + (vipInfo.abtest ? `,${vipInfo.abtest}` : ''),
      fv_abtest: initParams.abtest || ''
    }
    let newWin = null
    const isGopay = payTypeOption.payType.toString() === '10021'

    // let newWin = getDevice() === 'pc' && !isGopay ? window.open('about:blank', '_blank') : window
    try {
      const doPayResult = await createOrder(params, lang, mod)
      // 加线上日志
      const addloggers = {
        popupDopay: {
          time: new Date(),
          uid: getUid(),
          abtest,
          doPayResult
        }
      }
      const loggers = localStorage.getItem('QiyiPlayerLogger')
      window.localStorage.setItem(
        'QiyiPlayerLogger',
        loggers + JSON.stringify(addloggers)
      )

      let nextStep = PAGE_MAP.queryPaymentStep.name
      let openUrl, mobile
      const orderInfo = {
        typeName: selectedPkg.vipTypeName,
        vipOrder: doPayResult.vipOrder,
        order: doPayResult.order,
        name: selectedPkg.text3,
        currencySymbol: selectedPkg.currencySymbol,
        price: selectedPkg.price,
        originalPrice: selectedPkg.originalPrice,
        autorenewTip: selectedPkg.autorenewTip,
        detail: selectedPkg.detail,
        cashierType: initParams.cashierType
      }

      if (isGopay && abtest === 'popup_test_a') {
        openUrl = getMobileUrl(doPayResult, selectedPkg, payTypeOption)
      } else if (isGopay && doPayResult.redirectUrl.match(/vip\/usermobile/)) {
        // gopay逻辑
        nextStep = PAGE_MAP.setMobileStep.name
        mobile = window.atob(
          decodeURIComponent(queryFromUrl(doPayResult.redirectUrl, 'mobile')) ||
            ''
        )
        doPayResult.order = decodeURIComponent(
          queryFromUrl(doPayResult.redirectUrl, 'order_code')
        )
        PAGE_MAP.queryPaymentStep.last = PAGE_MAP.setMobileStep.name
        // mobile = '1234567'
      } else if (doPayResult.redirectUrl) {
        openUrl = doPayResult.redirectUrl
      } else if (abtest === 'popup_test_a') {
        openUrl = getPayRediectUrl(orderInfo)
      } else {
        nextStep = PAGE_MAP.bankCardStep.name
      }

      if ((!isGopay && doPayResult.redirectUrl) || abtest === 'popup_test_a') {
        PAGE_MAP.queryPaymentStep.last = { 1: 'payTypeStep', 2: 'vodOrderStep' }
      }

      if (openUrl) {
        newWin =
          getDevice() === 'pc' ? window.open('about:blank', '_blank') : window
        newWin.location.href = openUrl
        newWin.opener = null
      }

      this.setState({
        step: nextStep,
        payTypeOption,
        resultInfo: {
          vipOrder: doPayResult.vipOrder,
          order: doPayResult.order,
          mobile,
          platform: initParams.bossCode,
          lang,
          mod,
          timeZone: getTimeZone(mod)
        },
        orderInfo
      })
    } catch (err) {
      console.log(err)
      if (getDevice() === 'pc' && newWin) {
        newWin.close()
      }
      if (err.message.match('timeout')) {
        this.setErrorPop('timeout')
      } else {
        this.setState({
          createPayTypeError: err.message || err.msg || ''
        })
      }
    }
  }

  getUserInfo = () => {
    let userInfo = {}
    if (isLogin()) {
      userInfo.uid = getUid()
      // 之前收银台的逻辑
      let lastGlobalLoginMsg = getCookies('lastGlobalLoginMsg')
      if (
        lastGlobalLoginMsg &&
        lastGlobalLoginMsg !== null &&
        lastGlobalLoginMsg !== ''
      ) {
        lastGlobalLoginMsg = window.JSON.parse(lastGlobalLoginMsg)
        userInfo.username = lastGlobalLoginMsg.realEmail || ''
      } else {
        userInfo.username = ''
      }
    } else {
      userInfo = {}
    }
    return userInfo
  }
  getParams = (
    orderInfo,
    inputInfo,
    iaTransactionId,
    gatewayRecommendation,
    lang
  ) => {
    const PAY_SUC_URL = 'vip/payResult?vipOrder='
    const userInfo = this.getUserInfo()
    const { order, vipOrder, partner, cashierType } = orderInfo || {}
    const { hasCard, boundCardInfo } = inputInfo
    const _defaultUrl = rebuildHttpsUrl(
      PAY_SUC_URL + vipOrder + `&cashierType=${cashierType}`
    )
    let _time = (inputInfo.cardInfo.expireTime || '').split('/')
    let params = {
      partner: partner || 'qiyue_global',
      sign: 'PCW',
      authcookie: 'PCW',
      order_code: order,
      uid: userInfo.uid || '',
      check3d_response_url: encodeURIComponent(_defaultUrl),
      dfp: (window.dfp && window.dfp.tryGetFingerPrint()) || '',
      userName: userInfo.username || '',
      agent_type: '',
      local_lang: lang,
      ds_version: 2,
      iaTransactionId,
      gatewayRecommendation,
      _t: new Date().getTime()
    }
    const secureParam = {
      ds_browse: window.navigator.userAgent,
      ds_SecureChallengeWindowSize: 'FULL_SCREEN',
      ds_colorDepth: window.screen.colorDepth,
      ds_javaEnabled: false,
      ds_language: window.navigator.language,
      ds_screenHeight: window.screen.height,
      ds_screenWidth: window.screen.width,
      ds_timeZone: new Date().getTimezoneOffset()
    }
    if (gatewayRecommendation === 'PROCEED') {
      params = Object.assign({}, params, secureParam)
    }
    if (!hasCard) {
      params = Object.assign({}, params, {
        card_issuer: inputInfo.cardInfo.cardType,
        card_num: inputInfo.cardInfo.bankNumber.replace(/\s/g, ''),
        expiry_month: (_time[0] || '').padStart(2, '0'),
        expiry_year: _time[1] || '',
        security_code: inputInfo.cardInfo.cvvInput
      })
    } else {
      params.card_id = boundCardInfo.cardId
    }

    return params
  }
  fetchBankData = async (
    orderInfo,
    inputInfo,
    iaTransactionId,
    gatewayRecommendation
  ) => {
    const { lang } = this.state
    const i18n = bankLangPkg[lang] || bankLangPkg['en_us']
    const options = { timeout: 40000, method: 'POST', credentials: true }
    options.params = qs.stringify(
      this.getParams(
        orderInfo,
        inputInfo,
        iaTransactionId,
        gatewayRecommendation,
        lang
      )
    )
    
    let result
    try {
      const data = await $http(bankDoPayInterface, options)
      if (data) {
        if (data.is_success === 'T' && parseInt(data.order_status, 10) === 1) {
          result = {
            msg: 'sus',
            status: 'ok'
          }
        } else if (data.code === 'CHECK_3d_ACS') {
          this.setState({
            show3DSModal: true,
            DSResData: data
          })
          // 到3d认证页面
          // const newWindow = isMobile
          //   ? window
          //   : window.open('about:blank', '_blank')
          // const frameReg = /<iframe.*<\/iframe>/
          // const version3ds1 = !!/redirectTo3ds1AcsSimple/.test(
          //   data.htmlBodyContent
          // )
          // isMobile && !version3ds1
          //   ? newWindow.document.write(data.htmlBodyContent)
          //   : newWindow.document.write(
          //       data.htmlBodyContent.replace(frameReg, '')
          //     )
          // const form =
          //   newWindow.document.querySelector('#threedsChallengeRedirect') ||
          //   newWindow.document.querySelector('#redirectTo3ds1AcsSimple')

          // if (form) {
          //   if (version3ds1) {
          //     const submitForm = newWindow.document.querySelector('form')
          //     submitForm && submitForm.submit()
          //     setTimeout(() => {
          //       newWindow.close()
          //     }, 5000)
          //   } else {
          //     if (!isMobile) {
          //       setTimeout(() => {
          //         newWindow.close()
          //       }, 5000)
          //     }
          //   }
          // } else {
          //   result = {
          //     msg: '__sysErr'
          //   }
          // }
          // // 跳到第三方等待页面
          // if (!isMobile) {
          //   this.goPayResult()
          //   this.setState({
          //     resultType: 'pending'
          //   })
          // }
        } else {
          result = {
            msg: data.msg || '__sysErr'
          }
        }
      } else {
        result = {
          msg: '__sysErr'
        }
      }
    } catch (error) {
      result = {
        msg: error.message.match('timeout') ? '__timeout' : '__sysErr'
      }
    }
    if (result && result.msg) {
      if (result.status === 'ok') {
        // 支付成功，跳到支付结果页
        this.setState({
          dopayError: '',
          resultType: 'pending',
          showPayResult: 'result',
          createOrdering: false
        })
      } else if (result.msg === '__timeout') {
        this.setState({
          resultType: 'neterr',
          showPayResult: 'result',
          createOrdering: false
        })
      } else if (result.msg === '__sysErr') {
        this.setState({
          dopayError: i18n.err_system,
          createOrdering: false
        })
      } else {
        this.setState({
          dopayError: result.msg,
          createOrdering: false
        })
      }
    }
  }

  // 3ds验证弹窗点击
  modalConfirm = (resData) => {
    const isMobile = getDevice() === 'mobile'
    const newWindow = window.open('about:blank', '_blank')
    const frameReg = /<iframe.*<\/iframe>/
    const version3ds1 = !!/redirectTo3ds1AcsSimple/.test(
      resData.htmlBodyContent
    )
    isMobile && !version3ds1
      ? newWindow.document.write(resData.htmlBodyContent)
      : newWindow.document.write(resData.htmlBodyContent.replace(frameReg, ''))
    const form =
      newWindow.document.querySelector('#threedsChallengeRedirect') ||
      newWindow.document.querySelector('#redirectTo3ds1AcsSimple')

    if (form) {
      if (version3ds1) {
        const submitForm = newWindow.document.querySelector('form')
        submitForm && submitForm.submit()
        setTimeout(() => {
          newWindow.close()
        }, 5000)
      } else {
        if (!isMobile) {
          setTimeout(() => {
            newWindow.close()
          }, 5000)
        }
      }
    } else {
      result = {
        msg: '__sysErr'
      }
    }
    // 跳到第三方等待页面
    this.goPayResult()
    this.setState({
      resultType: 'pending',
      show3DSModal: false
    })
  }

  modalCancel = () => {
    this.setState({
      createOrdering: false,
      show3DSModal: false
    })
  }

  fetchGopayData = async (mobile) => {
    const { lang, resultInfo, gopayLoading } = this.state
    if (gopayLoading) return

    const params = {
      sign: 'PCW',
      authcookie: 'PCW',
      order_code: resultInfo.order,
      dfp: (window.dfp && window.dfp.tryGetFingerPrint()) || '',
      mobile: mobile,
      local_lang: lang
    }

    try {
      // return
      this.setState({ gopayLoading: true })

      // return
      const res = await $http(SUBMIT_PAY_PHONE, { params })
      if (res.code === 'A00000') {
        const newwin =
          getDevice() === 'pc' ? window.open('about:blank', '_blank') : window
        newwin.location.href = res.redirectUrl
        this.setState({
          gopayError: '',
          createOrdering: false,
          gopayLoading: false
        })
        this.goPayResult() // setStep('queryPaymentStep')
      } else {
        // getDevice() === 'pc' && newwin.close()
        this.setState({
          gopayError: res.msg,
          createOrdering: false,
          gopayLoading: false
        })
      }
    } catch (err) {
      // getDevice() === 'pc' && newwin.close()
      this.setState({
        createOrdering: false,
        gopayLoading: false
      })
      console.log(err)
    }
  }
  // 请求
  fetchInitVerify = async (orderInfo, inputInfo) => {
    const { order } = orderInfo
    const options = { timeout: 40000 }
    const { hasCard, boundCardInfo } = inputInfo
    let params = {
      sign: 'PCW',
      authcookie: 'PCW',
      order_code: order,
      card_num: inputInfo.cardInfo.bankNumber.replace(/\s/g, '')
    }
    if (hasCard) {
      params = {
        sign: 'PCW',
        authcookie: 'PCW',
        order_code: order,
        card_id: boundCardInfo.cardId
      }
    }

    options.params = params
    try {
      // return
      const data = await $http(INIT_3DS_VERIFY, options)
      if (data.code === 'A00000') {
        const { iaTransactionId, redirectHtml, gatewayRecommendation } = data
        if (gatewayRecommendation === 'PROCEED') {
          let verifyDiv = document.createElement('div')
          verifyDiv.style.display = 'none'
          verifyDiv.innerHTML = redirectHtml
          document.body.append(verifyDiv)
          var element = document.getElementById('initiate3dsSimpleRedirectForm')
          if (element) {
            element.submit()
            element.remove()
          }
        }
        this.fetchBankData(
          orderInfo,
          inputInfo,
          iaTransactionId,
          gatewayRecommendation
        )
      } else {
        this.setState({
          dopayError: data.msg || 'Error',
          createOrdering: false
        })
      }
    } catch (err) {
      console.log(err)
    }
  }
  // **************
  newHandleCreateVipOrder = async (
    selectedPkg,
    payTypeOption,
    selectedCOU = {},
    inputInfo,
    payPrice
  ) => {
    const { lang, vipInfo, createOrdering } = this.state
    const mod = getCookies('mod') || 'intl'
    const initParams = this.state.params
    const abtest = vipInfo.groupCode
    const params = {
      pid: selectedPkg.pid,
      payType: payTypeOption.payType,
      amount: selectedPkg.amount,
      bossCode: initParams.bossCode,
      payAutoRenew: selectedPkg.payAutoRenew,
      aid: initParams.aid || '',
      fc: initParams.fc || '',
      fv: initParams.fv || '',
      abtest: abtest,
      fv_abtest: initParams.abtest || '',
      couponCode: selectedCOU.couponCode || '',
      partnerID: queryFromUrl(window.location.href, 'partnerID') || ''
    }
    let result = null
    let newWin = null
    const isGopay = payTypeOption.payType.toString() === '10021'
    const isMaster =
      payTypeOption.payType.toString() === '10010' ||
      payTypeOption.payType.toString() === '10009'
    if (createOrdering) return
    this.setState({
      createOrdering: true
    })
    const dopayT = Date.now()
    try {
      sendQosLog({
        diy_evt: 'pop_dopay_start',
        ce: window.globalVipCE
      })
    } catch (err) {
      console.log(err)
    }

    // return  v
    // let newWin = getDevice() === 'pc' && !isGopay ? window.open('about:blank', '_blank') : window
    try {
      const doPayResult = await createOrder(params, lang, mod)
      const addloggers = {
        popupDopay: {
          time: new Date(),
          uid: getUid(),
          abtest,
          doPayResult
        }
      }
      try {
        sendQosLog({
          diy_evt: 'pop_dopay_end',
          tm: Date.now() - dopayT,
          diy_success: 1,
          ce: window.globalVipCE,
          pay_type: payTypeOption.payType || ''
        })
      } catch (err) {
        console.log(err)
      }

      const loggers = localStorage.getItem('QiyiPlayerLogger')
      window.localStorage.setItem(
        'QiyiPlayerLogger',
        loggers + JSON.stringify(addloggers)
      )
      if (doPayResult.code === 'COUPON_STATUS_FROZEN') {
        this.setState({
          showFreeze: true,
          createOrdering: false
        })
        return result
      }

      let openUrl, mobile
      const orderInfo = {
        typeName: selectedPkg.vipTypeName,
        vipOrder: doPayResult.vipOrder,
        order: doPayResult.order,
        name: selectedPkg.text3,
        currencySymbol: selectedPkg.currencySymbol,
        price: selectedPkg.price,
        originalPrice: selectedPkg.originalPrice,
        autorenewTip: selectedPkg.autorenewTip,
        detail: selectedPkg.detail,
        cashierType: initParams.cashierType,
        payType: payTypeOption.payType,
        payPrice
      }
      // return orderCallback(orderInfo, false)
      // 银行卡逻辑
      this.setState({ orderInfo, dopayError: '' })
      if (isMaster) {
        this.fetchInitVerify(orderInfo, inputInfo)
        // return
        // this.fetchBankData(orderInfo, inputInfo)
      } else if (
        (isGopay && doPayResult.redirectUrl.match(/cashier\.iqiyi\.com/)) ||
        (isGopay && doPayResult.redirectUrl.match(/vip\/usermobile/))
      ) {
        orderInfo.order = decodeURIComponent(
          queryFromUrl(doPayResult.redirectUrl, 'order_code')
        )
        doPayResult.order = decodeURIComponent(
          queryFromUrl(doPayResult.redirectUrl, 'order_code')
        )
        mobile = window.atob(
          decodeURIComponent(queryFromUrl(doPayResult.redirectUrl, 'mobile')) ||
            ''
        )
        this.setState({
          showPayResult: 'gopay'
        })
        // this.fetchGopayData(orderInfo, inputInfo)
      } else if (doPayResult.redirectUrl) {
        openUrl = doPayResult.redirectUrl
        this.goPayResult()
      }
      if (openUrl) {
        newWin =
          getDevice() === 'pc' ? window.open('about:blank', '_blank') : window
        newWin.location.href = openUrl
        newWin.opener = null
      }

      this.setState({
        // step: nextStep,
        payTypeOption,
        resultInfo: {
          vipOrder: doPayResult.vipOrder,
          order: doPayResult.order,
          mobile,
          platform: initParams.bossCode,
          lang,
          mod,
          timeZone: getTimeZone(mod)
        }
      })
    } catch (err) {
      sendQosLog({
        diy_evt: 'pop_dopay_end',
        tm: Date.now() - dopayT,
        diy_success: 0,
        ce: window.globalVipCE,
        pay_type: payTypeOption.payType || '',
        diy_msg: 'dopay Err ==>' + err
      })
      if (getDevice() === 'pc' && newWin) {
        newWin.close()
      }
      if (err.message.match('timeout')) {
        this.setErrorPop('timeout')
        this.setState({
          resultType: 'neterr',
          showPayResult: 'result'
        })
      } else {
        this.setState({
          createPayTypeError: err.message || err.msg || '',
          dopayError: err.message || err.msg || ''
        })
      }
    }
  }

  handlePackageSubmit = async (selectedPkg) => {
    if (!isLogin()) {
      if (!window.sdkPackManager) {
        await initLogin()
      } else if (!window.sdkPackManager.globalLogin) {
        await window.sdkPackManager.initLogin()
      }
      window.isRefresh = () => {
        this.setStateInLocal()
        window.location.reload()
      }
      window.sdkPackManager.globalLogin.openLoginRegWindow({
        vipModalInfo: { productSetCode: selectedPkg.productSetCode }
      })
      // this.setStateInLocal()
      return
    }

    // const cookieValue = getCookies('QC005')
    // const last = cookieValue && cookieValue[cookieValue.length - 1]
    // let jumpTest = false
    // if (Number(last)) {
    //   jumpTest = last < 5
    // } else {
    //   jumpTest = last < 'd'
    // }
    // const { vipInfo } = this.state

    // const jumpAgreement =
    //   vipInfo &&
    //   (!vipInfo.vipServiceAgreement ||
    //     vipInfo.isSelectVipServiceAgreement)
    // const hasOnlyPay = selectedPkg.payTypeOptions.length === 1

    let tempOptions = [...selectedPkg.payTypeOptions]
    const recommendIndex = selectedPkg.payTypeOptions.findIndex(
      (item) => item.recommend
    )
    if (recommendIndex === -1) {
      tempOptions[0].recommend = 1
    }
    this.setState({
      selectedPkg,
      currentPayTypeOptions: tempOptions
    })
    // if (jumpTest && jumpAgreement && hasOnlyPay) {
    //   this.handleCreateVipOrder(selectedPkg, selectedPkg.payTypeOptions[0])
    // } else {
    this.setState({
      step: PAGE_MAP.payTypeStep.name
    })
    // }
  }

  setErrorPop = (errorType) => {
    this.setState({
      isError: true,
      errorType
    })
  }

  setResultSuccess = (success) => {
    this.setState({
      resultSuccess: success
    })
  }
  setHideFreeze = () => {
    this.setState({
      showFreeze: false
    })
  }

  setIsAgree = (isAgree) => {
    this.setState({
      isAgree,
      checkErr: false
    })
  }

  changePayType = (index) => {
    const { currentPayTypeOptions } = this.state
    const _data = currentPayTypeOptions
    _data.forEach((item, itemIndex) => {
      if (itemIndex === index) {
        item.recommend = 1
      } else {
        item.recommend = 0
      }
    })
    this.setState({
      currentPayTypeOptions: [..._data]
    })
  }

  clearProductCode = () => {
    this.setState({
      productSetCode: undefined
    })
  }
  // 展示结果页面
  goPayResult = (step) => {
    this.setState({
      showPayResult: step || 'result',
      showFreeze: false,
      createOrdering: false,
      gopayError: '',
      dopayError: ''
    })
  }
  compSetProd = (prod) => {
    this.setState({
      selectedPkg: prod,
      currentPayTypeOptions: prod.payTypeOptions
    })
  }
  // 重置是否兑换
  resetRedeem = () => {
    this.setState({
      hasRedeemed: false
    })
  }
  goBack = (step) => {
    this.setState({
      showPayResult: step || 'pkgSelect'
    })
  }
  getContent() {
    const {
      resultInfo,
      step,
      errorType,
      isError,
      vipInfo,
      currentPayTypeOptions,
      createPayTypeError,
      isAgree,
      checkErr,
      mod,
      lang,

      params,
      payTypeOption,
      productSetCode,
      selectedPkg,
      orderInfo
    } = this.state
    const { vipLangPkg, cashierLangPkg, ptid, cashierType } = this.state.params
    const abtest = `popup_cashier,${getAbtest()}${
      this.state.params.abtest ? ',' + this.state.params.abtest : ''
    }`
    const selectedPayType =
      currentPayTypeOptions.find((item) => item.recommend) || {}
    const pbInfo = {
      cashier_type: 'popup',
      abtest: abtest,
      fc: params.fc || '',
      fv: params.fv || '',
      v_pid: selectedPkg.pid || '',
      v_prod: selectedPkg.productSetCode || '',
      pay_type: selectedPayType.payType || ''
    }

    const entranceId = 'iqiyi_intl_pcw_vip_checkout'
    const feedback = `//www.iq.com/intl-common/feedback.html?mod=${mod}&lang=${lang}&entranceId=${entranceId}&agentType=${hasAgentType()}&ptid=${ptid}&logType=14&subLogType=63`
    // 保持变量名字统一
    if (isError === true) {
      const errorVipLangPkg = vipLangPkg || {}
      if (errorType === 'timeout') {
        return (
          <TemplateErrorPop
            image="//www.iqiyipic.com/common/fix/global/api_network_error.png"
            title={errorVipLangPkg.pcashier_error_network}
            btnText={errorVipLangPkg.pcashier_error_retry}
            btnClick={() => {
              this.setState({
                isError: false,
                errorType: ''
              })
              this.setStep(step)
              if (step === PAGE_MAP.vipOrderStep.name) {
                this.getVipData()
              }
              if (step === PAGE_MAP.vodOrderStep.name) {
                // 这里走单点收银台
                this.setState({
                  isNewVip: false
                })
                this.getVodData()
              }
            }}
          />
        )
      } else {
        return (
          <TemplateErrorPop
            image="//www.iqiyipic.com/common/fix/global/api_oops.png"
            title={errorVipLangPkg.pcashier_error_errorOccur}
            btnText={errorVipLangPkg.pcashier_result_backPlan}
            btnClick={() => {
              this.setState({
                isError: false,
                errorType: ''
              })
              if (cashierType === 2) {
                // 这里走到单点收银台
                this.setStep(PAGE_MAP.vodOrderStep.name)
                this.setState({
                  isNewVip: false
                })
                this.getVodData()
              } else {
                this.setStep('vipOrderStep')
                this.getVipData()
              }
            }}
          />
        )
      }
    } else {
      if (step === PAGE_MAP.vodOrderStep.name) {
        return (
          <div className="content">
            <Vod
              data={this.state.vodVipInfo}
              error={this.state.createOrderError}
              vipLangPkg={vipLangPkg}
              cashierLangPkg={cashierLangPkg}
              handleSubmit={this.handleVodPayOrder}
              abtest={abtest}
              params={params}
            />
          </div>
        )
      }
      if (step === PAGE_MAP.vipOrderStep.name)
        return (
          <VipOrder
            vipInfo={this.state.vipInfo}
            selectedPkg={this.state.selectedPkg}
            setSelectPkg={(pkg) => {
              this.setState({
                selectedPkg: pkg
              })
            }}
            error={this.state.createOrderError}
            vipLangPkg={vipLangPkg}
            cashierLangPkg={cashierLangPkg}
            handleSubmit={this.handlePackageSubmit}
            vipType={this.state.params.vipType}
            pbInfo={pbInfo}
            productSetCode={productSetCode}
            clearProductCode={this.clearProductCode}
          />
        )
      if (step === PAGE_MAP.payTypeStep.name) {
        return (
          <PayType
            vipLangPkg={vipLangPkg}
            vipInfo={vipInfo}
            currentPayTypeOptions={currentPayTypeOptions}
            changePayType={this.changePayType}
            error={createPayTypeError}
            isAgree={isAgree}
            checkErr={checkErr}
            setIsAgree={this.setIsAgree}
            continueBtnHandle={this.handlePaytypeSubmit}
            pbInfo={pbInfo}
          />
        )
      }
      if (step === PAGE_MAP.setMobileStep.name) {
        return (
          <MobilePage
            resultInfo={resultInfo}
            vipLangPkg={vipLangPkg}
            vipInfo={vipInfo}
            selectedPkg={selectedPkg}
            setStep={this.setStep}
            abtest={abtest}
            params={params}
            mod={mod}
            lang={lang}
          />
        )
      }
      if (step === PAGE_MAP.queryPaymentStep.name) {
        return (
          <QueryPayment
            resultInfo={resultInfo}
            vipLangPkg={vipLangPkg}
            setStep={this.setStep}
            setErrorPop={this.setErrorPop}
            feedbackUrl={feedback}
            params={params}
            payIcon={payTypeOption.iconUrl}
            payName={payTypeOption.name}
            pbInfo={pbInfo}
          />
        )
      }
      if (step === PAGE_MAP.payResultStep.name) {
        return (
          <PayResult
            resultInfo={resultInfo}
            vipLangPkg={vipLangPkg}
            setStep={this.setStep}
            setErrorPop={this.setErrorPop}
            setResultSuccess={this.setResultSuccess}
            setHide={this.hide}
            params={params}
          />
        )
      }
      if (step === PAGE_MAP.bankCardStep.name) {
        // 取消跳转到行这一步
        return (
          <BankCard
            pageInfo={orderInfo}
            vipLangPkg={vipLangPkg}
            setErrorPop={this.setErrorPop}
            setStep={this.setStep}
            lang={lang}
            abtest={abtest}
            params={params}
            pbInfo={pbInfo}
          />
        )
      }
    }
    return ''
  }

  render() {
    const {
      step,
      visible,
      // checkout返回的会员信息
      vipInfo = {},
      showByGroup,
      isError,
      params,
      selectedPkg,
      currentPayTypeOptions,
      isNewVip,
      popLoading,
      showFreeze,
      showPayResult,
      resultType,
      defaultVipModalInfo,
      resultInfo,
      orderInfo,
      mod,
      lang,
      dopayError,
      gopayError,
      createOrdering,
      gopayLoading,
      hasRedeemed,
      showStayPop,
      stayData,
      userCards,
      show3DSModal,
      DSResData
    } = this.state
    const { ptid } = params
    // const { vipLangPkg, cashierLangPkg, ptid, cashierType } = this.state.params
    const entranceId = 'iqiyi_intl_pcw_vip_checkout'
    const feedback = `//www.iq.com/intl-common/feedback.html?mod=${mod}&lang=${lang}&entranceId=${entranceId}&agentType=${hasAgentType()}&ptid=${ptid}&logType=14&subLogType=63`
    if (!step) return null

    const { vipLangPkg, switchData, cashierLangPkg, swichData } = params || {}
    const { aniPopCount } = switchData || {}
    const abtest = `popup_cashier,${getAbtest()}${
      params.abtest ? ',' + params.abtest : ''
    }`

    const showBack = PAGE_MAP[step].last && !isError
    let rpage = ''
    switch (step) {
      case PAGE_MAP.vodOrderStep.name:
        rpage = 'web_tvod_casher'
        break
      case PAGE_MAP.vipOrderStep.name:
        rpage = 'cashier_popup'
        break
      case PAGE_MAP.payTypeStep.name:
        rpage = 'pay_type'
        break
      case PAGE_MAP.queryPaymentStep.name:
        if (params.cashierType === 2) {
          rpage = 'web_tvod_casher_redirection'
        } else {
          rpage = 'redirection'
        }
        break
      case PAGE_MAP.bankCardStep.name:
        rpage = 'bank_card_fill'
        break
      case PAGE_MAP.setMobileStep.name:
        rpage = 'gopay_getphone'
        break
      default:
        rpage = ''
        break
    }
    const selectedPayType =
      currentPayTypeOptions.find((item) => item.recommend) || {}
    const pbInfo = {
      cashier_type: 'popup',
      abtest: vipInfo ? vipInfo.groupCode : '' || '',
      fc: params.fc || '',
      fv: params.fv || '',
      v_pid: selectedPkg ? selectedPkg.pid : '',
      v_prod: selectedPkg ? selectedPkg.productSetCode : '',
      pay_type: selectedPayType ? selectedPayType.payType : ''
    }
    const pbInfoStr = qs.stringify(pbInfo) + '&bstp=56'
    return (
      <>
        <Script url="https://security.iq.com/static/iq/v2/verifycenter/js/verifycenter.js" />
        <link
          rel="stylesheet"
          type="text/css"
          href="//security.iq.com/static/iq/verifycenter/css/verifycenter.css"
        />
        <GlobalStyle>
          <Style visible={visible} showByGroup={showByGroup}>
            {isNewVip ? (
              <>
                {showPayResult === 'result' ? (
                  <div className="new-vip-container">
                    <NewPayResult
                      pbInfo={pbInfo}
                      pbInfoStr={pbInfoStr}
                      resultType={resultType}
                      resultInfo={resultInfo}
                      vipLangPkg={vipLangPkg}
                      feedbackUrl={feedback}
                      hide={this.hide}
                      goBack={this.goBack}
                      getVipData={this.newGetVipData}
                      setResultSuccess={this.setResultSuccess}
                    />
                  </div>
                ) : (
                  ''
                )}
                {showPayResult === 'gopay' ? (
                  <div className="new-vip-container">
                    <Gopay
                      pbInfo={pbInfo}
                      pbInfoStr={pbInfoStr}
                      vipLangPkg={vipLangPkg}
                      orderInfo={orderInfo}
                      mobile={resultInfo.mobile}
                      gopayError={gopayError}
                      gopayLoading={gopayLoading}
                      goPayResult={this.goPayResult}
                      hide={this.hide}
                      fetchGopayData={this.fetchGopayData}
                      clearError={this.clearError}
                    ></Gopay>
                  </div>
                ) : (
                  ''
                )}

                <div
                  className="new-vip-container"
                  style={{
                    display: showPayResult !== 'pkgSelect' ? 'none' : 'flex'
                  }}
                >
                  <div className="new-vip-header-bar">
                    <a
                      href="javascript:;"
                      className="close"
                      onClick={() => this.hide('main')}
                      rseat="close"
                      data-pb={`rpage=cashier_popup&block=extend_info&${pbInfoStr}`}
                    />
                  </div>

                  <TabContent
                    pbInfo={pbInfo}
                    pbInfoStr={pbInfoStr}
                    vipInfo={vipInfo}
                    userCards={userCards}
                    orderInfo={orderInfo}
                    vipLangPkg={vipLangPkg}
                    cashierLangPkg={cashierLangPkg}
                    popLoading={popLoading}
                    getVipData={this.newGetVipData}
                    handleCreateVipOrder={this.newHandleCreateVipOrder}
                    hasCoupon={switchData.i18n_switch_cashier_show_coupon}
                    showFreeze={showFreeze}
                    setHideFreeze={this.setHideFreeze}
                    defaultVipModalInfo={defaultVipModalInfo}
                    dopayError={dopayError}
                    aniPopCount={aniPopCount}
                    createOrdering={createOrdering}
                    compSetProd={this.compSetProd}
                    goPayResult={this.goPayResult}
                    resetRedeem={this.resetRedeem}
                    hasRedeemed={hasRedeemed}
                  />
                </div>
              </>
            ) : (
              <div className="container vod-container">
                <div className="header-bar">
                  <div
                    role="button"
                    tabIndex="0"
                    className="back"
                    data-pb={`rpage=${rpage}&block=extend_info&${pbInfoStr}`}
                    rseat="back"
                    onClick={() => {
                      let lastStep = PAGE_MAP[step].last
                      if (typeof lastStep === 'string') {
                        this.setStep(lastStep)
                      } else {
                        this.setStep(lastStep[params.cashierType || 1])
                      }
                    }}
                  >
                    {showBack && (
                      <>
                        {/* <ArrowLeft /> */}
                        <a
                          className="icon"
                          href="#"
                          data-pb={`rpage=${rpage}&block=extend_info&${pbInfoStr}`}
                          rseat="back"
                        />
                        {vipLangPkg.pcashier_back}
                      </>
                    )}
                  </div>
                  <a
                    href="#"
                    className="close"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      this.hide('main')
                    }}
                  />
                  {/* <CloseIcon /> */}
                </div>

                {/* <div className="content">{this.getContent()}</div> */}
                {this.getContent()}
              </div>
            )}
            {showStayPop ? (
              <StayPop
                pbInfo={pbInfo}
                stayData={stayData}
                hide={this.handleLeaveRetention}
                closeStayPop={this.handleStayRetention}
                currentRpage={rpage}
                currentFc={params.fc}
                currentFv={params.fv}
              ></StayPop>
            ) : (
              ''
            )}
            {show3DSModal ? (
              <DSModal
                vipLangPkg={vipLangPkg}
                confirmClk={() => this.modalConfirm(DSResData)}
                cancelClk={() => this.modalCancel()}
              />
            ) : (
              ''
            )}
          </Style>
        </GlobalStyle>
      </>
    )
  }
}

const mapStateToProps = (state) => ({
  state: state
})

const obj = createModal(
  connect(mapStateToProps, null, null, { forwardRef: true })(VipModal)
)
export const PopupVip = obj.PopupVip
export const createVipOrder = obj.createVipOrder
export const hideVipPopup = obj.hidePopup
