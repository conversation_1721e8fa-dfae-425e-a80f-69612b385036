/**
 * @desc 收银台挽留弹窗
 * <AUTHOR>
 * @time 2022-11-14
 * @feature  GLOBALREQ-10053
 */
import React, { useEffect, useState } from 'react'
import { removeProtocol } from '@/kit/url'
import { sendBlockPb, sendClickPb } from '@/utils/pingBack'
import StayPopWrapper from './style'

const StayPop = ({
  stayData,
  hide,
  closeStayPop,
  pbInfo,
  currentRpage = 'cashier_popup',
  currentFc = '',
  currentFv = ''
}) => {
  // 兼容新旧数据结构
  const isNewDataStructure = stayData.backgroundImages !== undefined

  let backgroundImages, retentionText, stayButtonText, leaveButtonText
  let positionCode, strategyCode, floatCode, block, fc

  // 图片加载失败状态管理
  const [failedImages, setFailedImages] = useState(new Set())
  const [showFallback, setShowFallback] = useState(false)

  if (isNewDataStructure) {
    // 新数据结构
    ({
      backgroundImages,
      retentionText,
      stayButtonText,
      leaveButtonText,
      positionCode,
      strategyCode,
      floatCode,
      block,
      fc
    } = stayData)
  } else {
    // 旧数据结构兼容
    const { coverDetail } = stayData
    const {
      backgroundPic,
      buttonText,
      titleText,
      trans_buttonText
    } = coverDetail || {}

    backgroundImages = backgroundPic ? [backgroundPic] : []
    retentionText = titleText
    stayButtonText = buttonText
    leaveButtonText = trans_buttonText

    // 从旧结构中解析打点参数
    block = stayData.block
    fc = stayData.fc

    const blockParts = (block || '').split('_')
    positionCode = blockParts[0] || ''
    strategyCode = blockParts[1] || ''
    floatCode = blockParts[2] || ''
  }

  // 构建打点参数
  const buildPingbackParams = (eventType, rseatParam) => {
    const params = {
      t: eventType,
      rpage: currentRpage,
      block: `${positionCode}_${strategyCode}_${floatCode}_block`,
      fc: currentFc || fc || '',
      fv: currentFv || ''
    }

    // 只有点击事件才添加rseat参数，展示事件不添加
    if (rseatParam) {
      params.rseat = rseatParam
    }

    return params
  }

  // 发送展示打点
  useEffect(() => {
    const params = buildPingbackParams(21) // 展示打点不传递rseat参数
    sendBlockPb(params.block, {
      rpage: params.rpage,
      fc: params.fc,
      fv: params.fv
    })
    return () => {}
  }, [])
  // 发送点击打点
  const sendRetentionClickPingback = (isStay) => {
    try {
      const rseatParam = isStay
        ? `${positionCode}_${strategyCode}_${floatCode}_rseat`
        : `${positionCode}_${strategyCode}_${floatCode}_rseat_leave`

      const params = buildPingbackParams(20, rseatParam)
      sendClickPb(params)
    } catch (error) {
      console.error('发送点击打点失败:', error)
    }
  }

  // 处理"留下"按钮点击
  const confirm = (e) => {
    // 阻止事件冒泡和默认行为，防止触发全局点击监听器
    e.preventDefault()
    e.stopPropagation()

    // 发送留下按钮点击打点
    sendRetentionClickPingback(true)

    // 执行留下逻辑
    closeStayPop(fc)
  }

  // 处理"离开"按钮点击
  const closeStay = (e) => {
    // 阻止事件冒泡和默认行为，防止触发全局点击监听器
    e.preventDefault()
    e.stopPropagation()

    // 发送离开按钮点击打点
    sendRetentionClickPingback(false)

    // 执行离开逻辑 - 关闭挽留弹窗和收银台弹窗
    hide('close')

    // 如果需要关闭收银台弹窗，可以调用额外的关闭方法
    // 这里可能需要根据实际的收银台弹窗关闭逻辑进行调整
    if (typeof closeStayPop === 'function') {
      closeStayPop('leave')
    }
  }

  // 处理图片加载失败
  const handleImageError = (index, imageUrl) => {
    console.error(`背景图片${index + 1}加载失败:`, imageUrl)

    setFailedImages(prev => {
      const newFailedImages = new Set(prev)
      newFailedImages.add(index)

      // 如果所有图片都加载失败，显示兜底背景
      if (backgroundImages && newFailedImages.size >= backgroundImages.length) {
        setShowFallback(true)
      }

      return newFailedImages
    })
  }


  // 创建CSS背景样式作为兜底
  const createFallbackBackground = (imageCount) => {
    const getBackgroundSize = () => {
      switch (imageCount) {
        case 1:
          return { width: '400px', height: '225px' }
        case 2:
          return { width: '400px', height: '267px' }
        case 3:
          return { width: '400px', height: '178px' }
        default:
          return { width: '400px', height: '225px' }
      }
    }

    const size = getBackgroundSize()
    return {
      ...size,
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: '#fff',
      fontSize: '16px',
      fontWeight: 'bold',
      textAlign: 'center',
      borderRadius: '8px 8px 0 0'
    }
  }

  // 阻止事件冒泡，防止触发全局点击监听器
  const handleContainerClick = (e) => {
    e.stopPropagation()
  }

  return (
    <StayPopWrapper imageCount={backgroundImages?.length || 0}>
      {/* 整体弹窗容器 */}
      <div className="retention-popup-container" onClick={handleContainerClick}>
        {/* 1. 背景图区域 */}
        <div className="retention-popup-image-area">
          {backgroundImages && backgroundImages.length > 0 ? (
            <>
              {!showFallback && backgroundImages.map((imageUrl, index) => (
                <img
                  key={index}
                  src={removeProtocol(imageUrl)}
                  alt={`retention background ${index + 1}`}
                  className="retention-popup-image"
                  style={{ display: failedImages.has(index) ? 'none' : 'block' }}
                  onLoad={() => {}}
                  onError={() => handleImageError(index, imageUrl)}
                />
              ))}
              {/* CSS背景作为兜底，当所有图片都加载失败时显示 */}
              {showFallback && (
                <div
                  className="retention-popup-image-fallback"
                  style={createFallbackBackground(backgroundImages.length)}
                >
                  VIP Member retention
                </div>
              )}
            </>
          ) : (
            <div className="retention-popup-image-placeholder" style={createFallbackBackground(0)}>
              VIP Member retention
            </div>
          )}
        </div>

        {/* 2. 合并的内容区域 - 包含描述和按钮 */}
        <div className="retention-popup-content-area">
          {/* 2.1 描述区域 */}
          <div className="retention-popup-description-block">
            {retentionText && (
              <div className="retention-popup-description-text">
                {retentionText}
              </div>
            )}
          </div>

          {/* 2.2 按钮区域 */}
          <div className="retention-popup-actions-block">
            {/* 继续购买按钮 */}
            <button
              className="retention-popup-button-continue"
              onClick={confirm}
            >
              <span className="retention-popup-button-continue-text">{stayButtonText}</span>
            </button>

            {/* 放弃优惠按钮 */}
            <button
              className="retention-popup-button-giveup"
              onClick={closeStay}
            >
              <span className="retention-popup-button-giveup-text">{leaveButtonText}</span>
            </button>
          </div>
        </div>
      </div>
    </StayPopWrapper>
  )
}
export default StayPop
